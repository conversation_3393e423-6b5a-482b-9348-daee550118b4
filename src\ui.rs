use eframe::{egui, <PERSON><PERSON>, <PERSON>ame};

pub struct MyApp {
    pub hero_name: String,
}

impl Default for MyApp {
    fn default() -> Self {
        Self {
            hero_name: String::new(),
        }
    }
}


fn get_dummy_hex_text() -> String {
    // Simulated hex dump
    let bytes = b"The quick brown fox jumps over the lazy dog.";
    bytes
        .chunks(16)
        .enumerate()
        .map(|(i, chunk)| {
            format!(
                "{:08X}: {:<48}  {}\n",
                i * 16,
                chunk.iter().map(|b| format!("{:02X} ", b)).collect::<String>(),
                String::from_utf8_lossy(chunk)
            )
        })
        .collect()
}



impl App for MyApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut Frame) {
        // Optional: White background
        // ctx.set_visuals(egui::Visuals::light());

        // --- Top Control Row ---
        egui::TopBottomPanel::top("top_panel").show(ctx, |ui| {
            ui.with_layout(egui::Layout::left_to_right(egui::Align::Center), |ui| {
                if ui.button("Load Game").clicked() {
                    println!("Load Game clicked");
                }

                ui.add_space(8.0);

                if ui.button("Find Hero").clicked() {
                    println!("Searching for '{}'", self.hero_name);
                }

                ui.add_space(8.0);

                let save_button_width = ui.spacing().interact_size.x * 2.0;
                let desired_text_width = ui.available_width() - save_button_width - 8.0;

                ui.add(
                    egui::TextEdit::singleline(&mut self.hero_name)
                        .desired_width(desired_text_width.max(50.0)),
                );

                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                    if ui.button("Save Game File").clicked() {
                        println!("Save Game clicked");
                    }
                });
            });
        });

        // --- Hex Viewer Placeholder ---
        egui::CentralPanel::default().show(ctx, |ui| {
            // Allocate 40% of available height
            let desired_height = ui.available_height() * 0.4;

            ui.allocate_ui(
                egui::vec2(ui.available_width(), desired_height),
                |ui| {
                    ui.group(|ui| {
                        ui.label("Hex Viewer:");
                        ui.add(
                            egui::TextEdit::multiline(&mut get_dummy_hex_text())
                                .font(egui::TextStyle::Monospace)
                                .desired_rows(10)
                                .desired_width(f32::INFINITY)
                                .lock_focus(true)
                                .code_editor()
                                .cursor_at_end(true)
                                .interactive(false), // read-only for now
                        );
                    });
                },
            );
        });
    }
}
