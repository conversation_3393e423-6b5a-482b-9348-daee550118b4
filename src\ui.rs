use eframe::{egui, <PERSON>pp, Frame};
use std::fs;
use std::path::PathBuf;

pub struct MyApp {
    pub hero_name: String,
    pub loaded_file_content: Option<Vec<u8>>,
    pub loaded_file_path: Option<PathBuf>,
    pub search_text: String,
    pub hero_data: Option<Vec<u8>>,
}

impl Default for MyApp {
    fn default() -> Self {
        Self {
            hero_name: String::new(),
            loaded_file_content: None,
            loaded_file_path: None,
            search_text: String::new(),
            hero_data: None,
        }
    }
}


fn get_dummy_hex_text() -> String {
    // Simulated hex dump
    let bytes = b"The quick brown fox jumps over the lazy dog.";
    bytes_to_hex_string(bytes)
}

fn bytes_to_hex_string(bytes: &[u8]) -> String {
    bytes
        .chunks(16)
        .enumerate()
        .map(|(i, chunk)| {
            // Create hex string with proper padding
            let hex_part = chunk.iter().map(|b| format!("{:02X} ", b)).collect::<String>();
            // Ensure hex part is exactly 48 characters (16 bytes * 3 chars each = 48)
            let padded_hex = format!("{:<48}", hex_part);

            // Create ASCII part, replacing non-printable characters with '.'
            let ascii_part = chunk.iter()
                .map(|&b| if b >= 32 && b <= 126 { b as char } else { '.' })
                .collect::<String>();

            format!("{:08X}: {}  {}\n", i * 16, padded_hex, ascii_part)
        })
        .collect()
}



impl App for MyApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut Frame) {
        // Optional: White background
        // ctx.set_visuals(egui::Visuals::light());

        // --- Top Control Row ---
        egui::TopBottomPanel::top("top_panel").show(ctx, |ui| {
            ui.with_layout(egui::Layout::left_to_right(egui::Align::Center), |ui| {
                if ui.button("Load Game").clicked() {
                    if let Some(path) = rfd::FileDialog::new()
                        .add_filter("All Files", &["*"])
                        .pick_file()
                    {
                        match fs::read(&path) {
                            Ok(content) => {
                                self.loaded_file_content = Some(content);
                                self.loaded_file_path = Some(path.clone());
                                println!("Loaded file: {:?}", path);
                            }
                            Err(e) => {
                                println!("Error reading file: {}", e);
                            }
                        }
                    }
                }

                ui.add_space(8.0);

                if ui.button("Find Hero").clicked() {
                    println!("Searching for '{}'", self.hero_name);
                }

                ui.add_space(8.0);

                let save_button_width = ui.spacing().interact_size.x * 2.0;
                let desired_text_width = ui.available_width() - save_button_width - 8.0;

                ui.add(
                    egui::TextEdit::singleline(&mut self.hero_name)
                        .desired_width(desired_text_width.max(50.0)),
                );

                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                    if ui.button("Save Game File").clicked() {
                        println!("Save Game clicked");
                    }
                });
            });
        });

        // --- Main Content Area ---
        egui::CentralPanel::default().show(ctx, |ui| {
            ui.vertical(|ui| {
                // --- Hex Viewer Section ---
                // Allocate 40% of available height for hex viewer
                let total_height = ui.available_height();
                let hex_viewer_height = total_height * 0.6;
                let button_height = 40.0; // Reserve space for buttons
                let available_hex_height = hex_viewer_height.min(total_height - button_height - 20.0); // 20px spacing

                ui.allocate_ui(
                    egui::vec2(ui.available_width(), available_hex_height),
                    |ui| {
                        ui.group(|ui| {
                            // Display file path if available
                            if let Some(path) = &self.loaded_file_path {
                                ui.label(format!("File: {}", path.display()));
                            } else {
                                ui.label("Hex Viewer:");
                            }

                            // Get hex content to display
                            let hex_content = if let Some(content) = &self.loaded_file_content {
                                bytes_to_hex_string(content)
                            } else {
                                get_dummy_hex_text()
                            };

                            // Calculate available height for the text area (subtract space for the label)
                            let available_height = ui.available_height() - 30.0; // Reserve space for label

                            egui::ScrollArea::vertical()
                                .max_height(available_height)
                                .show(ui, |ui| {
                                    ui.add(
                                        egui::TextEdit::multiline(&mut hex_content.clone())
                                            .font(egui::TextStyle::Monospace)
                                            .desired_width(f32::INFINITY)
                                            .lock_focus(true)
                                            .code_editor()
                                            .cursor_at_end(true)
                                            .interactive(false), // read-only for now
                                    );
                                });
                        });
                    },
                );

                // Add some spacing
                ui.add_space(10.0);

                // --- Hero Action Buttons Row ---
                ui.horizontal(|ui| {
                    // Calculate button width to take full window width
                    let available_width = ui.available_width();
                    let button_width = (available_width - 8.0) / 2.0; // 8px spacing between buttons

                    if ui.add_sized([button_width, 35.0], egui::Button::new("Extract Hero")).clicked() {
                        println!("Extract Hero clicked");
                    }

                    ui.add_space(8.0);

                    if ui.add_sized([button_width, 35.0], egui::Button::new("Write Hero")).clicked() {
                        println!("Write Hero clicked");
                    }
                });

                // Add some spacing
                ui.add_space(10.0);

                // --- Hero Management Buttons Row ---
                ui.horizontal(|ui| {
                    if ui.button("Load Hero").clicked() {
                        println!("Load Hero clicked");
                    }

                    ui.add_space(8.0);

                    if ui.button("Find in Block").clicked() {
                        println!("Searching for: '{}'", self.search_text);
                    }

                    ui.add_space(8.0);

                    // Calculate remaining width for text field and save button
                    let save_button_width = ui.spacing().interact_size.x * 2.0;
                    let desired_text_width = ui.available_width() - save_button_width - 8.0;

                    ui.add(
                        egui::TextEdit::singleline(&mut self.search_text)
                            .hint_text("Enter text to search...")
                            .desired_width(desired_text_width.max(100.0)),
                    );

                    ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                        if ui.button("Save Hero").clicked() {
                            println!("Save Hero clicked");
                        }
                    });
                });

                // Add some spacing
                ui.add_space(10.0);

                // --- Second Hex Editor (Hero Data) ---
                ui.group(|ui| {
                    ui.label("Hero Data:");

                    // Get hero hex content to display
                    let hero_hex_content = if let Some(content) = &self.hero_data {
                        bytes_to_hex_string(content)
                    } else {
                        "No hero data loaded...".to_string()
                    };

                    // Calculate remaining height for the second hex editor
                    let remaining_height = ui.available_height() - 20.0; // Reserve some space

                    egui::ScrollArea::vertical()
                        .max_height(remaining_height)
                        .show(ui, |ui| {
                            ui.add(
                                egui::TextEdit::multiline(&mut hero_hex_content.clone())
                                    .font(egui::TextStyle::Monospace)
                                    .desired_width(f32::INFINITY)
                                    .lock_focus(true)
                                    .code_editor()
                                    .cursor_at_end(true)
                                    .interactive(false), // read-only for now
                            );
                        });
                });
            });
        });
    }
}
