use eframe::egui;
mod ui;

fn main() -> Result<(), eframe::Error> {
    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([700.0, 320.0])       // initial size
            .with_min_inner_size([400.0, 320.0]),   //  minimum size allowed
        ..Default::default()
    };

    eframe::run_native(
        "My eframe App",
        options,
        Box::new(|_cc| Box::new(ui::MyApp::default())),
    )
}
